{
    // Use o IntelliSense para saber mais sobre os atributos possíveis.
    // Focalizar para exibir as descrições dos atributos existentes.
    // Para obter mais informações, acesse: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'crab-dlna'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=crab-dlna",
                    "--package=crab-dlna"
                ],
                "filter": {
                    "name": "crab-dlna",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'crab-dlna' play",
            "cargo": {
                "args": [
                    "build",
                    "--bin=crab-dlna",
                    "--package=crab-dlna"
                ],
                "filter": {
                    "name": "crab-dlna",
                    "kind": "bin"
                }
            },
            "args": [
                "-b",
                "play",
                "-q",
                "Kodi",
                "sample/video.mp4"
            ],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'crab-dlna' list",
            "cargo": {
                "args": [
                    "build",
                    "--bin=crab-dlna",
                    "--package=crab-dlna"
                ],
                "filter": {
                    "name": "crab-dlna",
                    "kind": "bin"
                }
            },
            "args": [
                "-b",
                "list"
            ],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'crab-dlna'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=crab-dlna",
                    "--package=crab-dlna"
                ],
                "filter": {
                    "name": "crab-dlna",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        }
    ]
}