//! DLNA metadata generation for crab-dlna
//!
//! This module handles the creation of DLNA-compatible metadata XML
//! for media files, including subtitle support.

use crate::{
    config::{DEFAULT_DLNA_VIDEO_TITLE, DLNA_INSTANCE_ID},
    error::Result,
    media::MediaStreamingServer,
};
use xml::escape::escape_str_attribute;

/// Builds the metadata XML for the media content
pub fn build_metadata(streaming_server: &MediaStreamingServer) -> Result<String> {
    let subtitle_uri = streaming_server.subtitle_uri();

    match subtitle_uri {
        Some(subtitle_uri) => {
            let metadata = format!(
                r###"
                    <DIDL-Lite xmlns="urn:schemas-upnp-org:metadata-1-0/DIDL-Lite/"
                                xmlns:dc="http://purl.org/dc/elements/1.1/" 
                                xmlns:upnp="urn:schemas-upnp-org:metadata-1-0/upnp/" 
                                xmlns:dlna="urn:schemas-dlna-org:metadata-1-0/" 
                                xmlns:sec="http://www.sec.co.kr/" 
                                xmlns:xbmc="urn:schemas-xbmc-org:metadata-1-0/">
                        <item id="0" parentID="-1" restricted="1">
                            <dc:title>{}</dc:title>
                            <res protocolInfo="http-get:*:video/{type_video}:" xmlns:pv="http://www.pv.com/pvns/" pv:subtitleFileUri="{uri_sub}" pv:subtitleFileType="{type_sub}">{uri_video}</res>
                            <res protocolInfo="http-get:*:text/srt:*">{uri_sub}</res>
                            <res protocolInfo="http-get:*:smi/caption:*">{uri_sub}</res>
                            <sec:CaptionInfoEx sec:type="{type_sub}">{uri_sub}</sec:CaptionInfoEx>
                            <sec:CaptionInfo sec:type="{type_sub}">{uri_sub}</sec:CaptionInfo>
                            <upnp:class>object.item.videoItem.movie</upnp:class>
                        </item>
                    </DIDL-Lite>
                    "###,
                DEFAULT_DLNA_VIDEO_TITLE,
                uri_video = streaming_server.video_uri(),
                type_video = streaming_server.video_type(),
                uri_sub = subtitle_uri,
                type_sub = streaming_server
                    .subtitle_type()
                    .unwrap_or_else(|| "unknown".to_string())
            );
            Ok(escape_str_attribute(metadata.as_str()).to_string())
        }
        None => {
            // Create metadata without subtitles
            let metadata = format!(
                r###"
                    <DIDL-Lite xmlns="urn:schemas-upnp-org:metadata-1-0/DIDL-Lite/"
                                xmlns:dc="http://purl.org/dc/elements/1.1/" 
                                xmlns:upnp="urn:schemas-upnp-org:metadata-1-0/upnp/">
                        <item id="0" parentID="-1" restricted="1">
                            <dc:title>{}</dc:title>
                            <res protocolInfo="http-get:*:video/{type_video}:">{uri_video}</res>
                            <upnp:class>object.item.videoItem.movie</upnp:class>
                        </item>
                    </DIDL-Lite>
                    "###,
                DEFAULT_DLNA_VIDEO_TITLE,
                uri_video = streaming_server.video_uri(),
                type_video = streaming_server.video_type(),
            );
            Ok(escape_str_attribute(metadata.as_str()).to_string())
        }
    }
}

/// Builds the SetAVTransportURI payload
pub fn build_setavtransporturi_payload(
    streaming_server: &MediaStreamingServer,
    metadata: &str,
) -> String {
    format!(
        r#"
        <InstanceID>{}</InstanceID>
        <CurrentURI>{}</CurrentURI>
        <CurrentURIMetaData>{}</CurrentURIMetaData>
        "#,
        DLNA_INSTANCE_ID,
        streaming_server.video_uri(),
        metadata
    )
}
