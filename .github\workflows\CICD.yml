# This workflow was inspired by the CICD from sharkdp/bat:
# https://github.com/sharkdp/bat/blob/master/.github/workflows/CICD.yml)

name: CI/CD

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop

env:
  CARGO_TERM_COLOR: always

jobs:

  checks-and-test:

    name: Run checks and test
    runs-on: ubuntu-latest

    steps:

      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Install rust toolchain
        uses: actions-rs/toolchain@v1
        with:
            target: ${{ matrix.job.target }}
            toolchain: stable
            override: true
            components: rustfmt, clippy

      - name: Check syntax with `cargo check`
        uses: actions-rs/cargo@v1
        with:
          command: check

      - name: Check good practices with `cargo clippy`
        uses: actions-rs/cargo@v1
        with:
          command: clippy
          args: --locked --all-targets --all-features

      - name: Check formatting with `cargo fmt`
        uses: actions-rs/cargo@v1
        with:
          command: fmt
          args: --all -- --check

      - name: Run tests
        uses: actions-rs/cargo@v1
        with:
          command: test
          args: --locked

  # FIXME: this job is not running in pull requests
  build-and-package:

    name: Build and Package - ${{ matrix.job.os }} (${{ matrix.job.target }})
    if: ${{ (github.ref == 'refs/heads/main') || (github.base_ref == 'refs/heads/main') }}
    runs-on: ${{ matrix.job.os }}
    strategy:
      fail-fast: false
      matrix:
        job:
          - { os: ubuntu-20.04, target: x86_64-unknown-linux-musl      , use-cross: true }
          - { os: ubuntu-20.04, target: i686-unknown-linux-musl        , use-cross: true }
          - { os: ubuntu-20.04, target: arm-unknown-linux-musleabihf   , use-cross: true }
          - { os: ubuntu-20.04, target: armv7-unknown-linux-musleabihf , use-cross: true }
          - { os: macos-11 , target: x86_64-apple-darwin            }
          - { os: windows-2019, target: x86_64-pc-windows-msvc         }
          - { os: windows-2019, target: i686-pc-windows-msvc           }

    steps:

      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Check for release
        id: is-release
        shell: bash
        run: |
          unset IS_RELEASE ; if [[ $GITHUB_REF = "refs/heads/main" ]]; then IS_RELEASE='true' ; fi
          echo ::set-output name=IS_RELEASE::${IS_RELEASE}

      - name: Install rust toolchain
        uses: actions-rs/toolchain@v1
        with:
            target: ${{ matrix.job.target }}
            toolchain: stable
            override: true

      - name: Build   
        uses: actions-rs/cargo@v1
        with:
          use-cross: ${{ matrix.job.use-cross }}
          command: build
          args: --release --all-features --target=${{ matrix.job.target }}

      - name: Extract crate information
        shell: bash
        run: |
          echo "PROJECT_NAME=$(sed -n 's/^name = "\(.*\)"/\1/p' Cargo.toml | head -n1)" >> $GITHUB_ENV
          echo "PROJECT_VERSION=$(sed -n 's/^version = "\(.*\)"/\1/p' Cargo.toml | head -n1)" >> $GITHUB_ENV

      - name: Create tarball package with the binary
        id: package
        if: steps.is-release.outputs.IS_RELEASE
        shell: bash
        run: |
          # Figure out suffix of binary
          EXE_suffix=""
          case ${{ matrix.job.target }} in
            *-pc-windows-*) EXE_suffix=".exe" ;;
          esac;

          # Figure out suffix of archive
          PKG_suffix=".tar.gz"
          case ${{ matrix.job.target }} in 
            *-pc-windows-*) PKG_suffix=".zip" ;; 
          esac;

          BIN_NAME="${PROJECT_NAME}${EXE_suffix}"
          BIN_PATH="target/${{ matrix.job.target }}/release/${BIN_NAME}"

          PKG_BASENAME=${PROJECT_NAME}-v${PROJECT_VERSION}-${{ matrix.job.target }}
          PKG_NAME=${PKG_BASENAME}${PKG_suffix}

          PKG_STAGING="result-${{ matrix.job.target }}/package"
          ARCHIVE_DIR="${PKG_STAGING}/${PKG_BASENAME}/"

          mkdir -p "${ARCHIVE_DIR}"

          # Binary
          cp "$BIN_PATH" "$ARCHIVE_DIR"

          # README and LICENSE files
          cp "README.md" "LICENSE-MIT" "LICENSE-APACHE" "$ARCHIVE_DIR"

          # base compressed package
          pushd "${PKG_STAGING}/" >/dev/null
          case ${{ matrix.job.target }} in
            *-pc-windows-*) 7z -y a "${PKG_NAME}" "${PKG_BASENAME}"/* | tail -2 ;;
            *) tar czf "${PKG_NAME}" "${PKG_BASENAME}"/* ;;
          esac;
          popd >/dev/null

          # Let subsequent steps know where to find the compressed package
          echo ::set-output name=PKG_PATH::"${PKG_STAGING}/${PKG_NAME}"

      - name: Upload tarball to release artifacts
        if: steps.is-release.outputs.IS_RELEASE
        uses: actions/upload-artifact@v3
        with:
          name: release-artifacts
          path: ${{ steps.package.outputs.PKG_PATH }}
          retention-days: 3


  github-release:

    name: Create GitHub release
    needs: build-and-package
    if: ${{ github.ref == 'refs/heads/main' }}
    runs-on: ubuntu-latest

    steps:

      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Extract crate information
        shell: bash
        run: |
          echo "PROJECT_NAME=$(sed -n 's/^name = "\(.*\)"/\1/p' Cargo.toml | head -n1)" >> $GITHUB_ENV
          echo "PROJECT_VERSION=$(sed -n 's/^version = "\(.*\)"/\1/p' Cargo.toml | head -n1)" >> $GITHUB_ENV

      - name: Check if a tag for the current version already exists
        uses: mukunku/tag-exists-action@v1.0.0
        id: check_release
        with: 
          tag: v${{ env.PROJECT_VERSION }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Download release artifacts
        if: ${{ steps.check_release.outputs.exists == 'false' }}
        uses: actions/download-artifact@v3
        with:
          name: release-artifacts
          path: downloaded-release-artifacts

      - name: Upload binaries to release
        if: ${{ steps.check_release.outputs.exists == 'false' }}
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: downloaded-release-artifacts/*
          tag: v${{ env.PROJECT_VERSION }}
          body: "crab-dlna v${{ env.PROJECT_VERSION }}"
          overwrite: true
          file_glob: true
